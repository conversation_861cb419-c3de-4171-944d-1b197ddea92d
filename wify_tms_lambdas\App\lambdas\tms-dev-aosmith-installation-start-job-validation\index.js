const axios = require("axios");

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/TMSIndiaCaseStartJob";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQFvD4Q_4a8Ha9RrUj5Zff_DSNXm0Ie7E4vXBjFEAUqrTuOGxxMLRfnh1BeEoAdbcIPMDtancBjIkF4XFbbnrS36VpvTN";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

const convertAttachmentToPublicUrl = (internalUrl) => {
  if (internalUrl) {
    const string = internalUrl; //"org_14/capture_image_1124406008470300_uaJ1IeWPIghOVE60.png";
    return `${filesPrefixUrl}${string}`;
  } else {
    return "";
  }
};

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "Error please contact admin";

  console.log("event form_data", event.form_data);
  console.log("event request_data", event.request_data);

  const fieldVsKey = {
    netsuiteOrderId: "79a88c7b-c64f-46c4-a277-bc80efa1c154",
    autoScannedBarcodeId: "8aefeefc-e0dd-45bf-8003-7870a8963156",
    manualEnteredBarcodeId: "f7b8d82d-faf5-488e-bb49-a2c67605c56a",
    beforeImageId: "84fc9160-eb45-4e93-ac7d-42a5cd96e5d1",
    barcodeImage: "49f14758-e06b-42bb-ba6d-175e71dffa9a",
  };
  const netsuiteOrderId = event.request_data[fieldVsKey.netsuiteOrderId];
  // console.log('netsuiteOrderId',netsuiteOrderId)
  const autoScannedBarcode = event.form_data[fieldVsKey.autoScannedBarcodeId];
  const manualEnteredBarcode =
    event.form_data[fieldVsKey.manualEnteredBarcodeId];
  const beforeImage = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey.beforeImageId]?.[0]
  );
  const barcodeImage = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey.barcodeImage]?.[0]
  );

  const attachments = event.form_data.attachments?.general || [];
  const finalAttachments = attachments.map((url) => filesPrefixUrl + url);

  console.log("autoScannedBarcode", autoScannedBarcode);
  console.log("manualEnteredBarcode", manualEnteredBarcode);

  let data = {
    SeriesAndSerialCombination: autoScannedBarcode || manualEnteredBarcode,
    isScanned: autoScannedBarcode ? "true" : "false", // "true" when scanned, "false" when manually entered (Salesforce expects string boolean)
    case_number: netsuiteOrderId,
    BeforeInvoiceUrl: beforeImage,
    custrecord_before_image_tms: beforeImage,
    barcode_img: barcodeImage,
    Start_job_attachments: finalAttachments,
  };

  console.log("Salesforce api call data", data);
  try {
    const response = await makeRequest(data);
    console.log("Salesforce api response", response.data); // Handle the API response data

    const salesforceData = response.data.data?.[0] || response.data;

    if (netSuiteData && netSuiteData.message) {
      let { message, responseCode, status } = netSuiteData;
      responseMessage = message;
      console.log(
        "message,responseCode,status : ",
        message,
        responseCode,
        status
      );
      if (status == "Success") {
        responseStatus = true; //
      } else {
        responseMessage = message;
      }
    } else {
      // this is a fatal error from salesforce
      // the response is not as per documentation rxd
      responseMessage = JSON.stringify(response.data);
    }
  } catch (error) {
    logAxiosError(error);
    // handle error
  }

  if (autoScannedBarcode == undefined && manualEnteredBarcode == undefined) {
    responseMessage =
      "You have already updated this status, you cannot update it again.";
  }

  // if(data.remarks)
  // TODO implement
  const response = {
    status: responseStatus,
    message: responseMessage,
  };
  return response;
};

// handler({});

exports.handler = handler;
