const axios = require("axios");

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/TMSIndiaCaseClosure";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQFvD4Q_4a8Ha9RrUj5Zff_DSNXm0Ie7E4vXBjFEAUqrTuOGxxMLRfnh1BeEoAdbcIPMDtancBjIkF4XFbbnrS36VpvTN";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

const convertAttachmentToPublicUrl = (internalUrl) => {
  if (internalUrl) {
    const string = internalUrl; //"org_14/capture_image_1124406008470300_uaJ1IeWPIghOVE60.png";
    return `${filesPrefixUrl}${string}`;
  } else {
    return "";
  }
};

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "No response received from Netsuite";

  console.log("event form_data", event.form_data);
  console.log("event request_data", event.request_data);

  const optionsFieldMap = {
    "Part not available": "0c4dd182-9846-47d4-b66d-7e43ee85831d",
    "Customer appointment": "66c961d0-b8a4-42b4-86c9-cb8d07a386e1",
    "PJP Not available": "e53472ac-9a49-4506-811f-1a83c07c513d",
    "Natural Calamity": "b384f524-173f-40a1-a2f0-2317dd1d748b",
    "Send Back to Call Centre": "b71b6482-4d6d-48a3-a518-7fc67cb807d7",
    "IT/ Waterapps Support": "a2b3f76b-763c-4cd9-8b5b-685fa32a5cff",
    "Customer not contactable": "88815544-6a1a-41c1-9937-4477c9f5583e",
    "Pending for Approval/ Replacement / Refund":
      "1b30d505-6193-4222-a786-0e9651d6fe9a",
    Others: "d3b98870-162a-4257-85a5-525634abc075",
  };

  const pendingOptionsvsCode = {
    "Part not available": 1,
    "Customer appointment": 2,
    "PJP Not available": 3,
    "Natural Calamity": 4,
    "Send Back to Call Centre": 5,
    "IT/ Waterapps Support": 6,
    "Customer not contactable": 7,
    "Pending for Approval/ Replacement / Refund": 8,
    Others: 9,
  };
  const getKeyByValue = (object, value) => {
    return Object.keys(object).find((key) => object[key] === value);
  };
  const getOptionValueByKey = (key, optionsFieldMap, pendingOptionsvsCode) => {
    return pendingOptionsvsCode[getKeyByValue(optionsFieldMap, key)];
  };

  const fieldVsKey = {
    netsuiteOrderId: "79a88c7b-c64f-46c4-a277-bc80efa1c154",
    "Reason for pending": "5f362646-8243-4ed7-b26e-20eecbc98dd5",
  };
  const netsuiteOrderId = event.request_data[fieldVsKey.netsuiteOrderId];
  const attachments = event.form_data.attachments?.general || [];
  const finalAttachments = attachments.map((url) => filesPrefixUrl + url);

  const reasonForPending = getOptionValueByKey(
    event.form_data[fieldVsKey["Reason for pending"]],
    optionsFieldMap,
    pendingOptionsvsCode
  );
  console.log("reasonForPending", reasonForPending);

  let data = {
    case_id: netsuiteOrderId,
    status: 7, //7 is for pending
    reason_for_Pending: reasonForPending,
    Pending_attachments: finalAttachments,
    remarks: event.form_data.remarks,
  };

  console.log("salesforce api call data", data);
  try {
    const response = await makeRequest(data);
    console.log("salesforce api response", response.data); // Handle the API response data
    if (response.data && response.data.message) {
      let { status, responseCode, message } = response.data;
      responseMessage = message;
      console.log(
        "message,responseCode,details : ",
        message,
        responseCode,
        status
      );
      if (status == "success") {
        responseStatus = true; //
      }
    } else {
      // this is a fatal error from salesforce
      // the response is not as per documentation rxd
      responseMessage = JSON.stringify(response.data);
    }
  } catch (error) {
    logAxiosError(error);
    // handle error
  }

  // if(data.remarks)
  // TODO implement
  const response = {
    status: responseStatus,
    message: responseMessage,
  };
  return response;
};

// handler({});

exports.handler = handler;
