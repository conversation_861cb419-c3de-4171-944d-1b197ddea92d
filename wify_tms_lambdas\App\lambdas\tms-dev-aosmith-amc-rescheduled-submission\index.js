const axios = require("axios");

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/IndiaAMCReschedule";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQFvD4Q_4a8Ha9RrUj5Zff_DSNXm0Ie7E4vXBjFEAUqrTuOGxxMLRfnh1BeEoAdbcIPMDtancBjIkF4XFbbnrS36VpvTN";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "Error please contact admin";

  console.log("event form_data", event.form_data);
  console.log("event request_data", event.request_data);

  const fieldVsKey = {
    netsuiteOrderId: "79a88c7b-c64f-46c4-a277-bc80efa1c154",
    date: "006b680a-2108-463d-8ecc-e43d3f84fdb1",
    start_time: "620569d8-9535-4a84-8446-9e04f25f5687",
    end_time: "406338ff-46e9-40ac-b5ce-4e4c8583feda",
  };
  const netsuiteOrderId = event.request_data[fieldVsKey.netsuiteOrderId];
  // console.log('netsuiteOrderId',netsuiteOrderId)

  const date = event.form_data[fieldVsKey.date];
  const start_time = event.form_data[fieldVsKey.start_time];
  const end_time = event.form_data[fieldVsKey.end_time];

  const attachments = event.form_data.attachments?.general || [];
  // const finalAttachments = attachments.map(url => filesPrefixUrl + url);

  let data = {
    request_type: "AMC_RESCHEDULE",
    date: date.split("T")[0],
    start_time: start_time,
    end_time: end_time,
    internal_id: netsuiteOrderId.split("/")[1],
    reason_for_reschedule: event.form_data.remarks,
  };

  console.log("salesforce api call data", data);
  try {
    const response = await makeRequest(data);
    console.log("salesforce api response", response.data); // Handle the API response data

    const salesforceData = response?.data[0] || response.data;

    if (salesforceData && salesforceData.message) {
      let { message, responseCode, status } = salesforceData;
      responseMessage = message;
      console.log(
        "message,responseCode,status : ",
        message,
        responseCode,
        status
      );
      if (status == "Success") {
        responseStatus = true; //
      } else {
        responseMessage = message;
      }
    } else {
      // this is a fatal error from salesforce
      // the response is not as per documentation rxd
      responseMessage = JSON.stringify(response.data);
    }
  } catch (error) {
    logAxiosError(error);
    // handle error
  }

  const response = {
    status: responseStatus,
    message: responseMessage,
  };
  return response;
};

// handler({});

exports.handler = handler;
