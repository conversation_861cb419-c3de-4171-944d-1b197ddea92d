const axios = require("axios");

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/IndiaAMCRetrigger";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQFvD4Q_4a8Ha9RrUj5Zff_DSNXm0Ie7E4vXBjFEAUqrTuOGxxMLRfnh1BeEoAdbcIPMDtancBjIkF4XFbbnrS36VpvTN";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

const updateSrvcReqonTMS = async (data) => {
  const url = "https://uat-tms.wify.co.in/v1/brands/order/379";
  const token = "9D5918AC-DAA3-470D-9F12-83F13F23036E";

  const config = {
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
      accept: "*/*",
    },
  };

  return await axios.post(url, JSON.stringify(data), config);
};

const convertAttachmentToPublicUrl = (internalUrl) => {
  if (internalUrl) {
    const string = internalUrl; //"org_14/capture_image_1124406008470300_uaJ1IeWPIghOVE60.png";
    return `${filesPrefixUrl}${string}`;
  } else {
    return "";
  }
};

function getCurrentTimestampInIST() {
  const now = new Date();
  const istOffset = 5.5 * 60 * 60 * 1000; // IST is UTC+5:30
  return now.getTime() + istOffset;
}

function canTriggerAMCLogic(triggerTimestampArr, currentTimestamp) {
  console.log("currentTimestamp in ist", currentTimestamp);
  const thirtySecondsInMillis = 30 * 1000;

  // Calculate the start of today in IST
  const startOfToday = new Date(currentTimestamp);
  console.log("startOfToday", startOfToday);
  startOfToday.setHours(0, 0, 0, 0);
  console.log("startOfToday", startOfToday);

  // Filter the triggers that happened today in IST
  console.log("triggerTimestampArr", triggerTimestampArr);
  const todayTriggers = triggerTimestampArr.filter((timestamp) => {
    const triggerTime = new Date(
      new Date(timestamp).getTime() + 5.5 * 60 * 60 * 1000
    );
    return triggerTime >= startOfToday;
  });
  console.log("todayTriggers", todayTriggers);

  // Check if we already have 5 triggers today
  if (todayTriggers.length >= 5) {
    return {
      status: false,
      message: "Retrigger limit reached for the day",
    };
  }

  // Check if the current trigger is at least 30 seconds after the last trigger
  if (triggerTimestampArr.length > 0) {
    console.log("why is this not running");
    const lastTriggerTime = new Date(
      triggerTimestampArr[triggerTimestampArr.length - 1]
    ).getTime();
    console.log(
      "lastTriggerTime ",
      lastTriggerTime,
      currentTimestamp,
      thirtySecondsInMillis,
      (currentTimestamp - lastTriggerTime) / 1000
    );
    if (currentTimestamp - lastTriggerTime < thirtySecondsInMillis) {
      return {
        status: false,
        message: "Please wait for 30 seconds before retriggering",
      };
    }
  }

  return {
    status: true,
    message: "Can retrigger amc link",
  };
}

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "Error please contact admin";

  console.log("event form_data", event.form_data);
  console.log("event request_data", event.request_data);

  const fieldVsKey = {
    netsuiteOrderId: "79a88c7b-c64f-46c4-a277-bc80efa1c154",
    customerMobileNo: "59f924f9-9b3f-435f-83ab-d2fc217f8572",
  };
  const netsuiteOrderId = event.request_data[fieldVsKey.netsuiteOrderId];
  const customerMobileNo = event.form_data[fieldVsKey.customerMobileNo];
  let amcRetriggerCalls = event.request_data?.amc_retrigger_calls || [];
  console.log("amcRetriggerCalls", amcRetriggerCalls);

  let data = {
    request_type: "AMC_RETRIGGER",
    mobile_number: customerMobileNo,
    rfs_for_amc_internal_id: netsuiteOrderId.split("/")[1],
    comments_for_retrigger: event.form_data.remarks,
  };

  let currentTimestamp = getCurrentTimestampInIST();
  const date = new Date(currentTimestamp);
  const timestampFrReqData = date.toISOString();

  let canRetrigger = canTriggerAMCLogic(amcRetriggerCalls, currentTimestamp);
  console.log("canRetrigger ", canRetrigger);
  if (canRetrigger.status) {
    console.log("salesforce api call data", data);
    try {
      const response = await makeRequest(data);
      console.log("salesforce api response", response.data); // Handle the API response data

      const salesforceData = response?.data?.[0] || response.data;

      if (salesforceData && salesforceData.message) {
        let { message, responseCode, status } = salesforceData;
        responseMessage = message;
        console.log(
          "message,responseCode,status : ",
          message,
          responseCode,
          status
        );
        if (status == "Success") {
          responseStatus = true;
        } else {
          responseMessage = message;
        }
      } else {
        // this is a fatal error from salesforce
        // the response is not as per documentation rxd
        responseMessage = JSON.stringify(response.data);
      }
    } catch (error) {
      logAxiosError(error);
    }
  } else {
    responseMessage = canRetrigger.message;
  }

  if (responseStatus) {
    amcRetriggerCalls.push(timestampFrReqData);
    console.log("yeti amcRetriggerCalls", amcRetriggerCalls);
    let updateSrvcReqData = {
      batch_data: [
        {
          "79a88c7b-c64f-46c4-a277-bc80efa1c154": netsuiteOrderId,
          amc_retrigger_calls: amcRetriggerCalls,
        },
      ],
    };
    //call tms api to check details
    try {
      console.log("TMS api data", JSON.stringify(updateSrvcReqData));
      const response = await updateSrvcReqonTMS(updateSrvcReqData);
      console.log("TMS api response", response.data); // Handle the API response data
      const tmsResponse = response.data;
      if (tmsResponse.status == "success") {
        console.log("update hogaya", JSON.stringify(response.data));
        responseStatus = true;
        responseMessage = JSON.stringify(response.message);
      } else {
        responseStatus = true;
        responseMessage = JSON.stringify(response.message);
        console.log("cannot run api");
      }
    } catch (error) {
      // logAxiosError(error);
      console.error("Error in handler:", error);
      responseMessage = "An error occurred: " + error.message;
    }
  }

  // if(data.remarks)
  // TODO implement
  const response = {
    status: responseStatus,
    message: responseMessage,
  };
  return response;
};

// handler({});

exports.handler = handler;
