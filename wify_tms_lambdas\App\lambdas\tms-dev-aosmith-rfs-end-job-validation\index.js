const axios = require("axios");

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/TMSIndiaOrderClosure";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQFvD4Q_4a8Ha9RrUj5Zff_DSNXm0Ie7E4vXBjFEAUqrTuOGxxMLRfnh1BeEoAdbcIPMDtancBjIkF4XFbbnrS36VpvTN";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

const convertAttachmentToPublicUrl = (internalUrl) => {
  if (internalUrl) {
    const string = internalUrl; //"org_14/capture_image_1124406008470300_uaJ1IeWPIghOVE60.png";
    return `${filesPrefixUrl}${string}`;
  } else {
    return "";
  }
};

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "No response received from Salesforce";

  console.log("event form_data", event.form_data);
  console.log("event request_data", event.request_data);
  console.log("event triggered_lambda_key", event.triggered_lambda_key);

  let lineItemsMap = [
    {
      item_label: "Item 1",
      qty_label: "Quantity 1",
      price_label: "Price 1",
    },
    {
      item_label: "Item 2",
      qty_label: "Quantity 2",
      price_label: "Price 2",
    },
    {
      item_label: "Item 3",
      qty_label: "Quantity 3",
      price_label: "Price 3",
    },
    {
      item_label: "Item 4",
      qty_label: "Quantity 4",
      price_label: "Price 4",
    },
  ];

  const fieldVsKey = {
    netsuiteOrderId: "79a88c7b-c64f-46c4-a277-bc80efa1c154",
    "Item 1": "8ff5d752-be57-4acb-9aaf-1d1269f698ad",
    "Quantity 1": "e205f833-0fc8-4abb-91d0-54b7fc885ba0",
    "Price 1": "881b64b7-b05a-489d-b355-69ed9f5a86f8",
    "Item 2": "8f85e97b-140a-4b2b-814b-a475e271f606",
    "Quantity 2": "92bac71b-166d-4481-8378-4d6fbb399e74",
    "Price 2": "037b5651-455e-44c3-b499-4768d1908dde",
    "Item 3": "cc44b7a4-4020-407e-b384-09309cdd15b8",
    "Quantity 3": "748d4784-b580-46e2-81e5-bcd046d18cd7",
    "Price 3": "d38b3373-6bb5-4fdb-a249-a3ebaa7d851a",
    "Item 4": "732c3f5f-9a0b-4c3f-a6a1-2090d966bfaf",
    "Quantity 4": "3769c480-4a47-49d4-b38a-ca9be55dc0de",
    "Delivery Charges": "ebedad39-d282-4b80-aca0-e61931562f81",
    "Total RFS Value": "86e3d911-11cb-4964-8a88-92b23b7b973a",
    "Order Closure Confirmation Code": "4410f19a-2e0f-4471-8501-664a10e783dd",
    "After Service Image": "d81871e0-9948-4e99-9a40-6e3552c41231",
    "Invoice Image": "29dc49d2-1898-45f7-a5b9-bd830b3f09e1",
  };
  const netsuiteOrderId = event.request_data[fieldVsKey.netsuiteOrderId];
  const afterServiceImage = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey["After Service Image"]]?.[0]
  );
  const invoiceImage = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey["Invoice Image"]]?.[0]
  );
  const attachments = event.form_data.attachments?.general || [];
  const finalAttachments = attachments.map((url) => filesPrefixUrl + url);
  let rfsValue = event.form_data[fieldVsKey["Total RFS Value"]];

  let itemDataFrNetsuite = [];

  lineItemsMap.map((singleLineItemMap) => {
    const item = event.form_data[fieldVsKey[singleLineItemMap.item_label]];
    if (item) {
      itemDataFrNetsuite.push({
        item,
        quantity: event.form_data[fieldVsKey[singleLineItemMap.qty_label]],
        price: event.form_data[fieldVsKey[singleLineItemMap.price_label]] + "",
      });
    }
  });

  let data = {
    rfsid: netsuiteOrderId,
    Delivery_dispostion: 1, //1 is for delivered
    isDelivered: "T", //sending true for delivered
    Rfs_Value: rfsValue,
    Items_Data: itemDataFrNetsuite,
    Delivery_Charges: event.form_data[fieldVsKey["Delivery Charges"]],
    order_closure_confirmation_code:
      event.form_data[fieldVsKey["Order Closure Confirmation Code"]],
    After_Service_Image: afterServiceImage,
    Invoice_Image: invoiceImage,
    remarks: event.form_data.remarks,
    Close_job_attachments: finalAttachments,
  };

  console.log("salesforce api call data", data);
  try {
    const response = await makeRequest(data);
    console.log("salesforce api response", response.data); // Handle the API response data
    const salesforceResp = response.data;
    if (salesforceResp && salesforceResp.status) {
      let { status, responseCode, message } = response.data;
      responseMessage = message;
      console.log(
        "message,responseCode,details : ",
        message,
        responseCode,
        status
      );
      if (status == "success") {
        responseStatus = true; //
      }
    } else {
      // this is a fatal error from salesforce
      // the response is not as per documentation rxd
      responseMessage = JSON.stringify(response.data);
    }
  } catch (error) {
    logAxiosError(error);
    // handle error
  }

  // if(data.remarks)
  // TODO implement
  const response = {
    status: responseStatus,
    message: responseMessage,
  };
  console.log("checking response msg", responseMessage);
  return response;
};

// handler({});

exports.handler = handler;
