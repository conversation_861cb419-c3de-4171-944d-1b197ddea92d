const axios = require("axios");

const atmMasterToken =
  "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************.Fvo7wHDaHJhmnlJXZM-4-MkU9sS3w3-Vnpi4CL159RI";

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/TMSIndiaDeviceToItemCase";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQFvD4Q_4a8Ha9RrUj5Zff_DSNXm0Ie7E4vXBjFEAUqrTuOGxxMLRfnh1BeEoAdbcIPMDtancBjIkF4XFbbnrS36VpvTN";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

const modifyFieldInMeta = (meta, fieldKey, newKeyValueObj) => {
  meta.map((singleField, index) => {
    if (singleField.key == fieldKey) {
      singleField = {
        ...singleField,
        ...newKeyValueObj,
      };
      meta[index] = singleField;
    }
  });
};

const getObjByKeyFrmArray = (array = [], key, value) => {
  return array.filter((singleObj) => singleObj[key] == value)?.[0];
};

function getUniqueEntries(array, keyName) {
  const uniqueEntries = [];
  const uniqueKeys = {};

  for (let i = 0; i < array.length; i++) {
    const obj = array[i];
    const key = obj[keyName];

    if (!uniqueKeys[key]) {
      // Add entry to the uniqueEntries array
      uniqueEntries.push(obj);
      // Add key to the uniqueKeys object
      uniqueKeys[key] = true;
    }
  }

  return uniqueEntries;
}

function getFieldOptions(data, field, int_id, extra_keys = []) {
  const fieldOptions = [];
  const existingKeys = new Set();

  data.forEach((obj) => {
    if (!existingKeys.has(obj[int_id])) {
      const option = { label: obj[field], value: obj[int_id] };

      extra_keys.forEach((key) => {
        option[key] = obj[key];
      });

      fieldOptions.push(option);
      existingKeys.add(obj[int_id]);
    }
  });

  return fieldOptions;
}

function getKeyByValue(data, value) {
  return Object.keys(data).find((key) => data[key] === value);
}

function hideEverythingOnTheFormExcept(meta, exceptionLabels = []) {
  meta.map((singleField, index) => {
    singleField = {
      ...singleField,
      hide: !exceptionLabels.includes(singleField.label),
    };
    meta[index] = singleField;
  });
}

function makeFieldsVisibleAndMandatory(
  meta,
  labelToKeyMap,
  mandatoryLabels = []
) {
  mandatoryLabels.forEach((singleField) => {
    modifyFieldInMeta(meta, labelToKeyMap[singleField], {
      required: true,
      hide: false,
    });
  });
}

function hideMultipleFields(meta, labelToKeyMap, mandatoryLabels = []) {
  mandatoryLabels.forEach((singleField) => {
    modifyFieldInMeta(meta, labelToKeyMap[singleField], {
      hide: true,
    });
  });
}

async function getOptionsFromATMMasterFr(params) {
  console.log("ATM api call params", params);
  const url = "https://dev-hul-dataset-service.wify.co.in/hul/get_all_datasets";
  const headers = {
    accept: "application/json",
    Authorization: atmMasterToken,
  };
  let atmMasterResponse = await axios.get(url, { headers, params });
  console.log("ATM api response", atmMasterResponse);
  return atmMasterResponse.data;
}

function complaintTitleIsInstallation(allValues, labelToKeyMap, titleOptions) {
  const selectedOPtion = getObjByKeyFrmArray(
    titleOptions,
    "value",
    allValues[labelToKeyMap["Complaint Title"]]
  );
  console.log("complaintTitleIsInstallation selectedOPtion", selectedOPtion);
  if (
    selectedOPtion?.premium_installation_module == "Yes" ||
    selectedOPtion?.premium_installation_module == "true" ||
    selectedOPtion?.premium_installation_module == "True"
  ) {
    return true;
  } else {
    return false;
  }
}

function actionTakenIsReplaced(allValues, labelToKeyMap, actionTakenOptions) {
  const regex = /\breplaced\b/i;
  const selectedOPtion = getObjByKeyFrmArray(
    actionTakenOptions,
    "value",
    allValues[labelToKeyMap["Action Taken"]]
  );
  console.log("actionTakenIsReplaced selectedOPtion", selectedOPtion);
  if (
    selectedOPtion?.faulty_item_mandatory == "Yes" ||
    selectedOPtion?.faulty_item_mandatory == "true" ||
    selectedOPtion?.faulty_item_mandatory == "True"
  ) {
    return true;
  } else {
    return false;
  }
}

function modifyLineItemFields(
  meta,
  lineItemDetails,
  labelToKeyMap,
  updateMeta
) {
  Object.keys(lineItemDetails).map((singleLabel) => {
    modifyFieldInMeta(
      meta,
      labelToKeyMap[lineItemDetails[singleLabel]],
      updateMeta
    );
  });
}

function getNumberOfItemsToShow(
  allValues,
  numberOfLineItemsOptionsMap,
  labelToKeyMap
) {
  const selectedRadio = allValues[labelToKeyMap["Number of line items"]];
  const radioOption = getObjByKeyFrmArray(
    numberOfLineItemsOptionsMap,
    "value",
    selectedRadio
  );
  return radioOption?.label;
}

function warrantyStatusIsInWarranty(warrantyStatus) {
  if (warrantyStatus == "22d14259-bf74-451e-8aed-d11a3890bd07") {
    return true;
  } else {
    return false;
  }
}

function prefillIsClaimableForLineItem(
  manipulatedFieldValues,
  allValues,
  labelToKeyMap,
  singleLineItem,
  prefillVal,
  meta
) {
  if (allValues[labelToKeyMap[singleLineItem.is_claimable]] == undefined) {
    const optionsForIsClaimable = getObjByKeyFrmArray(
      meta,
      "key",
      labelToKeyMap[singleLineItem.is_claimable]
    )?.options;
    const keyForPrefillVal = getObjByKeyFrmArray(
      optionsForIsClaimable,
      "label",
      prefillVal
    ).value;
    manipulatedFieldValues[labelToKeyMap[singleLineItem.is_claimable]] =
      keyForPrefillVal;
  }
}

function allValuesIsInitial(allValues) {
  const emptyAllValues = {
    attachments: {},
    mic_files: {},
    camera_files: {},
  };
  return JSON.stringify(allValues) == JSON.stringify(emptyAllValues);
}

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "No response received from Salesforce 1";

  const { meta, allValues, changedValues, request_data, currentMeta } = event;

  console.log("event", event);

  const labelToKeyMap = {};
  const headingsKeyMap = {};

  meta.map((singleField) => {
    labelToKeyMap[singleField.label] = singleField.key;
  });

  meta.map((singleField) => {
    if (singleField.cust_component == "legend") {
      headingsKeyMap[singleField.cust_component_value] = singleField.key;
    }
  });

  console.log("meta", JSON.stringify(event.meta));
  // console.log('event form_data',event.form_data);
  console.log("event request_data", event.request_data);

  let hidePremiumInstallationModule;

  let lineItemsMap = [
    {
      item_label: "Item 1",
      is_claimable: "Is claimable 1",
      qty_label: "Quantity 1",
      price_label: "Price 1",
    },
    {
      item_label: "Item 2",
      is_claimable: "Is claimable 2",
      qty_label: "Quantity 2",
      price_label: "Price 2",
    },
    {
      item_label: "Item 3",
      is_claimable: "Is claimable 3",
      qty_label: "Quantity 3",
      price_label: "Price 3",
    },
    {
      item_label: "Item 4",
      is_claimable: "Is claimable 4",
      qty_label: "Quantity 4",
      price_label: "Price 4",
    },
  ];
  let lineItemOptions = [];
  let complaintTitleOptions = [];
  let isFirstTime =
    Object.keys(changedValues).length == 0 && allValuesIsInitial(allValues);

  const manipulatedFieldValues = {};

  //to be decided from service req meta
  const fieldVsKey = {
    netsuiteOrderId: "79a88c7b-c64f-46c4-a277-bc80efa1c154",
    deviceCategoryId: "baa73b9d-0f53-43e7-85e0-34b7f7c38c20",
    warrantyStatus: "c632a3c7-519d-4a8d-a440-b43a16cb0718",
  };
  const netsuiteOrderId = request_data[fieldVsKey.netsuiteOrderId];
  const deviceCategoryId = request_data[fieldVsKey.deviceCategoryId];
  const warrantyStatus = request_data[fieldVsKey.warrantyStatus];

  const numberOfLineItemsOptionsMap = getObjByKeyFrmArray(
    meta,
    "key",
    labelToKeyMap["Number of line items"]
  ).options;

  let titleOptions = [];
  let actionTakenOptions = [];
  // check if the API based data is already there
  if (isFirstTime) {
    // Get the data from API

    // just show title
    hideEverythingOnTheFormExcept(meta, ["Complaint Title", "Item 1"]);
    // get options for title using device category id from service request
    let atmResponseFrTitle = await getOptionsFromATMMasterFr({
      dev_cat_id: deviceCategoryId,
    });
    if (atmResponseFrTitle) {
      titleOptions = getFieldOptions(
        atmResponseFrTitle,
        "title_name",
        "title_int_id",
        ["premium_installation_module"]
      );
    }
    // get line items for faulty items
    let data = {
      deviceCategory: deviceCategoryId,
      Rfs_id: netsuiteOrderId,
    };

    // manipulatedFieldValues[fieldVsKey['numberOfLineItems']] = numberOfLineItemsOptionsMap['1'];

    console.log("Netsuite api call data", data);

    let netsuiteLineItemOptions = [];
    try {
      const response = await makeRequest(data);
      console.log("Netsuite api response", response.data); // Handle the API response data
      if (response.data.status && response.data.message) {
        let { status, message } = response.data;
        if (status == "success") {
          netsuiteLineItemOptions = [...message];
        } else {
          console.log("error response from line item api", response.message);
        }
      } else {
        // this is a fatal error from netsuite
        // the response is not as per documentation rxd
        console.log("Fatal response received from Netsuite", response);
      }
    } catch (error) {
      logAxiosError(error);
      // handle error
    }

    const uniqueKeys = {};
    // console.log(`labelToKeyMap['Line item 1']`,labelToKeyMap['Line item 1'])
    lineItemOptions = netsuiteLineItemOptions.map((singleNetsuiteOption) => {
      let itemDescription = singleNetsuiteOption.item_description || "";
      return {
        value: singleNetsuiteOption.item_id,
        label: `${singleNetsuiteOption.itemName.trim()} ${itemDescription.trim()}`,
        price: singleNetsuiteOption.item_price,
      };
    });
    lineItemOptions = [...getUniqueEntries(lineItemOptions, "value")];
  } else {
    console.log("running else");
    let firstLineItemFieldMeta = getObjByKeyFrmArray(
      currentMeta,
      "key",
      labelToKeyMap[lineItemsMap[0].item_label]
    );
    console.log("firstLineItemFieldMeta", firstLineItemFieldMeta);
    lineItemOptions = [...firstLineItemFieldMeta.options];

    titleOptions = [
      ...getObjByKeyFrmArray(
        currentMeta,
        "key",
        labelToKeyMap["Complaint Title"]
      ).options,
    ];

    let descriptionOptions = [
      ...getObjByKeyFrmArray(currentMeta, "key", labelToKeyMap["Description"])
        .options,
    ];
    modifyFieldInMeta(meta, labelToKeyMap["Description"], {
      options: descriptionOptions,
    });

    let partNameOptions = [
      ...getObjByKeyFrmArray(currentMeta, "key", labelToKeyMap["Part name"])
        .options,
    ];
    modifyFieldInMeta(meta, labelToKeyMap["Part name"], {
      options: partNameOptions,
    });

    actionTakenOptions = [
      ...getObjByKeyFrmArray(currentMeta, "key", labelToKeyMap["Action Taken"])
        .options,
    ];
    modifyFieldInMeta(meta, labelToKeyMap["Action Taken"], {
      options: actionTakenOptions,
    });

    console.log("changed values", changedValues);
    // see if title is changed
    if (changedValues[labelToKeyMap["Complaint Title"]]) {
      // reset description, partename and action taken
      ["Description", "Part name", "Action Taken"].map((singelFieldLabel) => {
        manipulatedFieldValues[labelToKeyMap[singelFieldLabel]] = "";
      });
      // hide everything after description
      hideEverythingOnTheFormExcept(meta, [
        "Complaint Title",
        "Description",
        "Number of line items",
      ]);

      let atmResponseFrDescription = await getOptionsFromATMMasterFr({
        dev_cat_id: deviceCategoryId,
        title_int_id: allValues[labelToKeyMap["Complaint Title"]],
      });
      console.log("atmResponseFrDescription", atmResponseFrDescription);
      let descriptionOptions = [];
      if (atmResponseFrDescription) {
        descriptionOptions = getFieldOptions(
          atmResponseFrDescription,
          "desc_name",
          "desc_int_id"
        );
      }
      modifyFieldInMeta(meta, labelToKeyMap["Description"], {
        options: descriptionOptions,
      });
    }

    // see if description is changed
    if (changedValues[labelToKeyMap["Description"]]) {
      // reset description, partename and action taken
      ["Part name", "Action Taken"].map((singelFieldLabel) => {
        manipulatedFieldValues[labelToKeyMap[singelFieldLabel]] = "";
      });
      // hide everything after description
      hideEverythingOnTheFormExcept(meta, [
        "Complaint Title",
        "Description",
        "Part name",
        "Number of line items",
      ]);

      let atmResponseFrPartName = await getOptionsFromATMMasterFr({
        dev_cat_id: deviceCategoryId,
        title_int_id: allValues[labelToKeyMap["Complaint Title"]],
        desc_int_id: allValues[labelToKeyMap["Description"]],
      });
      let descriptionOptions = [];
      if (atmResponseFrPartName) {
        partNameOptions = getFieldOptions(
          atmResponseFrPartName,
          "part_parent",
          "part_int_id"
        );
      }
      modifyFieldInMeta(meta, labelToKeyMap["Part name"], {
        options: partNameOptions,
      });
    }

    // see if complaint title is changed
    if (changedValues[labelToKeyMap["Part name"]]) {
      // reset description, partename and action taken
      ["Action Taken"].map((singelFieldLabel) => {
        manipulatedFieldValues[labelToKeyMap[singelFieldLabel]] = "";
      });
      // hide everything after description
      hideEverythingOnTheFormExcept(meta, [
        "Complaint Title",
        "Description",
        "Part name",
        "Action Taken",
        "Number of line items",
      ]);

      let atmResponseFrActionTaken = await getOptionsFromATMMasterFr({
        dev_cat_id: deviceCategoryId,
        title_int_id: allValues[labelToKeyMap["Complaint Title"]],
        desc_int_id: allValues[labelToKeyMap["Description"]],
        part_int_id: allValues[labelToKeyMap["Part name"]],
      });
      let actionTakenOptions = [];
      if (atmResponseFrActionTaken) {
        actionTakenOptions = getFieldOptions(
          atmResponseFrActionTaken,
          "name",
          "internal_id",
          ["faulty_item_mandatory"]
        );
      }
      console.log("atmResponseFrActionTtaken", actionTakenOptions);
      modifyFieldInMeta(meta, labelToKeyMap["Action Taken"], {
        options: actionTakenOptions,
      });
    }
  }
  modifyFieldInMeta(meta, labelToKeyMap["Complaint Title"], {
    options: titleOptions,
  });

  modifyFieldInMeta(meta, headingsKeyMap["Faulty Item Details"], {
    hide: false,
  });

  //
  // if(!complaintTitleIsInstallation(allValues,labelToKeyMap,titleOptions)){
  //   hideEverythingOnTheFormExcept(meta,['Complaint Title','Description','Part name','Action Taken'])
  // }else
  if (complaintTitleIsInstallation(allValues, labelToKeyMap, titleOptions)) {
    modifyFieldInMeta(meta, headingsKeyMap["Premium Installation Module"], {
      hide: false,
    });
    modifyLineItemFields(meta, lineItemsMap[0], labelToKeyMap, {
      required: false,
    });
    makeFieldsVisibleAndMandatory(meta, labelToKeyMap, [
      "No. of stories building",
      "Customer residing on Floor no.",
      "Location of water tank or overhead tank",
      "Length of tube required(if more than 2m)",
      "Inlet water pressure",
      "PRV Installed",
      "Source of Sale",
      "Boosterpump fixed due to low I/P Water pressure",
      "Check the inlet water TDS level by TDS meter",
      "Output TDS level",
    ]);
  } else {
    modifyFieldInMeta(meta, headingsKeyMap["Premium Installation Module"], {
      hide: true,
    });
    hideMultipleFields(meta, labelToKeyMap, [
      "No. of stories building",
      "Customer residing on Floor no.",
      "Location of water tank or overhead tank",
      "Length of tube required(if more than 2m)",
      "Inlet water pressure",
      "PRV Installed",
      "Source of Sale",
      "Boosterpump fixed due to low I/P Water pressure",
      "Check the inlet water TDS level by TDS meter",
      "Output TDS level",
    ]);
  }
  if (actionTakenIsReplaced(allValues, labelToKeyMap, actionTakenOptions)) {
    modifyLineItemFields(meta, lineItemsMap[0], labelToKeyMap, {
      required: true,
    });
    hideMultipleFields(meta, labelToKeyMap, [
      "No. of stories building",
      "Customer residing on Floor no.",
      "Location of water tank or overhead tank",
      "Length of tube required(if more than 2m)",
      "Inlet water pressure",
      "PRV Installed",
      "Source of Sale",
      "Boosterpump fixed due to low I/P Water pressure",
      "Check the inlet water TDS level by TDS meter",
      "Output TDS level",
    ]);
  } else if (
    !actionTakenIsReplaced(allValues, labelToKeyMap, actionTakenOptions)
  ) {
    modifyLineItemFields(meta, lineItemsMap[0], labelToKeyMap, {
      hide: false,
    });
  }

  // hiding line items
  const numberOfITemsToSHow =
    getNumberOfItemsToShow(
      allValues,
      numberOfLineItemsOptionsMap,
      labelToKeyMap
    ) || 1;
  let lineItemTotal = 0;

  let alreadySelectedItems = [];
  lineItemsMap.map((singleLineItem, i) => {
    if (i < parseInt(numberOfITemsToSHow)) {
      modifyLineItemFields(meta, singleLineItem, labelToKeyMap, {
        hide: false,
        // required : true
      });
      // Setting line item options to line item 1
      modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.item_label], {
        options: lineItemOptions,
      });
      // Setting the price for individual selected line item
      let lineItem = allValues[labelToKeyMap[singleLineItem.item_label]];
      // Check if this item was already selected
      if (alreadySelectedItems.includes(lineItem)) {
        // reset this line item field
        lineItem = undefined;
        manipulatedFieldValues[labelToKeyMap[singleLineItem.item_label]] = "";
      } else {
        alreadySelectedItems.push(lineItem);
      }

      if (lineItem) {
        // set price
        let itemPrice = getObjByKeyFrmArray(
          lineItemOptions,
          "value",
          lineItem
        )?.price;

        const currentItemPriceInField =
          allValues[labelToKeyMap[singleLineItem.price_label]];
        if (currentItemPriceInField == undefined || itemPrice) {
          manipulatedFieldValues[labelToKeyMap[singleLineItem.price_label]] =
            itemPrice || 0;
        }

        modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.price_label], {
          disabled: itemPrice ? true : false,
        });
        // Adding to total
        let itemQty = allValues[labelToKeyMap[singleLineItem.qty_label]];

        if (itemQty != null && itemQty <= 0) {
          itemQty = 1;
          manipulatedFieldValues[labelToKeyMap[singleLineItem.qty_label]] = 1;
        }
        // checking if is claimable
        const optionsForIsClaimable = getObjByKeyFrmArray(
          meta,
          "key",
          labelToKeyMap[singleLineItem.is_claimable]
        ).options;
        const keyForYes = getObjByKeyFrmArray(
          optionsForIsClaimable,
          "label",
          "Yes"
        ).value;
        let isClaimable = warrantyStatusIsInWarranty(warrantyStatus);
        if (allValues[labelToKeyMap[singleLineItem.is_claimable]]) {
          isClaimable =
            allValues[labelToKeyMap[singleLineItem.is_claimable]] == keyForYes;
        }

        if (itemPrice && itemQty && !isClaimable) {
          lineItemTotal = lineItemTotal + itemPrice * itemQty;
        }
        // make the qty field not mandatory for item which is not selected
        modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.qty_label], {
          required: true,
        });
      } else {
        // make the qty field not mandatory for item which is not selected
        modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.qty_label], {
          required: false,
        });
        manipulatedFieldValues[labelToKeyMap[singleLineItem.qty_label]] = "";
        manipulatedFieldValues[labelToKeyMap[singleLineItem.price_label]] = "";
      }
    } else {
      modifyLineItemFields(meta, singleLineItem, labelToKeyMap, {
        hide: true,
        // required : true
      });
    }
    //prefilling is claimable
    console.log("warrantyStatus", warrantyStatus);
    if (warrantyStatusIsInWarranty(warrantyStatus)) {
      prefillIsClaimableForLineItem(
        manipulatedFieldValues,
        allValues,
        labelToKeyMap,
        singleLineItem,
        "Yes",
        meta
      );
    } else {
      prefillIsClaimableForLineItem(
        manipulatedFieldValues,
        allValues,
        labelToKeyMap,
        singleLineItem,
        "No",
        meta
      );
    }
  });

  // set line item count by default
  if (allValues[labelToKeyMap["Number of line items"]] == undefined) {
    manipulatedFieldValues[labelToKeyMap["Number of line items"]] =
      numberOfLineItemsOptionsMap[0].value;
  }

  let visitCharges = allValues[labelToKeyMap["Visit Charge"]];
  if (visitCharges > 0) {
    lineItemTotal = lineItemTotal + visitCharges;
  }

  manipulatedFieldValues[labelToKeyMap["Total Amount Collected"]] =
    lineItemTotal;
  // modifyFieldInMeta(meta,labelToKeyMap['Total Amount Collected'],{
  //   disabled : true
  // })

  modifyFieldInMeta(meta, labelToKeyMap["After service image"], {
    required: true,
  });

  modifyFieldInMeta(meta, labelToKeyMap["Service invoice image"], {
    required: true,
  });

  if (warrantyStatusIsInWarranty(warrantyStatus)) {
    modifyFieldInMeta(meta, labelToKeyMap["Device invoice image"], {
      required: true,
    });
  } else {
    modifyFieldInMeta(meta, labelToKeyMap["Device invoice image"], {
      required: false,
    });
  }

  responseStatus = true;
  responseMessage = "success";
  const response = {
    status: responseStatus,
    message: responseMessage,
    data: { meta, allValues, changedValues, manipulatedFieldValues },
  };
  console.log("final meta", meta);
  return response;
};

// handler({});

exports.handler = handler;
