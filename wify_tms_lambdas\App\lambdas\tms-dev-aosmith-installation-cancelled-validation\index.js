const axios = require("axios");

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/TMSIndiaCaseClosure";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQFvD4Q_4a8Ha9RrUj5Zff_DSNXm0Ie7E4vXBjFEAUqrTuOGxxMLRfnh1BeEoAdbcIPMDtancBjIkF4XFbbnrS36VpvTN";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};
const convertAttachmentToPublicUrl = (internalUrl) => {
  if (internalUrl) {
    const string = internalUrl; //"org_14/capture_image_1124406008470300_uaJ1IeWPIghOVE60.png";
    return `${filesPrefixUrl}${string}`;
  } else {
    return "";
  }
};

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "No response received from Netsuite";

  console.log("event form_data", event.form_data);
  console.log("event request_data", event.request_data);

  const optionsFieldMap = {
    "Not Installed - Installation Not Required":
      "1c3a7ed8-2bac-4773-8ef7-dc00a3731dc4",
    "Not Installed - Device in transit/not delivered":
      "1fb3b6e3-71b3-49f7-8277-a172844173d5",
    "Not Installed - Device not purchased":
      "1844c9f7-5b75-4873-a7c3-389dfa9f285b",
    "Not installed – Postponed": "8826bf05-5d3c-4ec7-82f8-2c7d8562c21b",
    "Not Installed - Device returned back":
      "a730f7d4-7dd5-45c8-94e9-4fc88338ab04",
    "Duplicate Request": "815cf0ea-3107-4670-b6f5-ea938bb1e5bc",
    "Not installed - High TDS": "01313cfe-4e6c-4180-a472-be1846b92149",
    "Not installed due to Low TDS": "fd489a37-205d-43a9-a5ca-a80d691f6814",
    "Not installed - High Pressure": "aeddb6a9-e3d3-4f87-9e96-8296db93bb72",
    "Not installed due to Low Pressure": "89a95175-a833-49d0-99f2-3b39607c432b",
    "Not Contactable & Address Not Found":
      "84e09ed1-aace-4ebf-9fca-e29dcfc263df",
  };

  const cancellationOptionsvsCode = {
    "Not Installed - Installation Not Required": 1,
    "Not Installed - Device in transit/not delivered": 2,
    "Not Installed - Device not purchased": 3,
    "Not installed – Postponed": 4,
    "Not Installed - Device returned back": 5,
    "Duplicate Request": 6,
    "Not installed - High TDS": 7,
    "Not installed due to Low TDS": 8,
    "Not installed - High Pressure": 9,
    "Not installed due to Low Pressure": 10,
    "Not Contactable & Address Not Found": 11,
  };

  const getKeyByValue = (object, value) => {
    return Object.keys(object).find((key) => object[key] === value);
  };
  const getOptionValueByKey = (
    key,
    optionsFieldMap,
    cancellationOptionsvsCode
  ) => {
    return cancellationOptionsvsCode[getKeyByValue(optionsFieldMap, key)];
  };

  const fieldVsKey = {
    netsuiteOrderId: "79a88c7b-c64f-46c4-a277-bc80efa1c154",
    "Reason for cancellation": "433bf9bd-61b3-4bb9-aa1e-8ec9fa8f90ac",
  };
  const netsuiteOrderId = event.request_data[fieldVsKey.netsuiteOrderId];

  const reasonForcancellation = getOptionValueByKey(
    event.form_data[fieldVsKey["Reason for cancellation"]],
    optionsFieldMap,
    cancellationOptionsvsCode
  );

  const attachments = event.form_data.attachments?.general || [];
  const finalAttachments = attachments.map((url) => filesPrefixUrl + url);

  let data = {
    case_id: netsuiteOrderId,
    status: 6, //6 is for cancelled
    reason_for_installation_cancellation: reasonForcancellation,
    Close_job_attachments: finalAttachments,
    remarks: event.form_data.remarks,
  };

  console.log("salesforce api call data", data);
  try {
    const response = await makeRequest(data);
    console.log("salesforce api response", response.data); // Handle the API response data
    if (response.data && response.data.message) {
      let { status, responseCode, message } = response.data;
      responseMessage = message;
      console.log(
        "message,responseCode,details : ",
        message,
        responseCode,
        status
      );
      if (status == "success") {
        responseStatus = true; //
      }
    } else {
      // this is a fatal error from salesforce
      // the response is not as per documentation rxd
      responseMessage = JSON.stringify(response.data);
    }
  } catch (error) {
    logAxiosError(error);
    // handle error
  }

  // if(data.remarks)
  // TODO implement
  const response = {
    status: responseStatus,
    message: responseMessage,
  };
  return response;
};

// handler({});

exports.handler = handler;
