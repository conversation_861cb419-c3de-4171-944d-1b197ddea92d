const axios = require("axios");

const updateSrvcReqonTMS = async (data) => {
  const url = "https://api-tms.wify.co.in/v1/brands/order/644";
  const token = "DE8F23EE-CFA9-408A-B945-29D3D985AE86";

  const config = {
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
      accept: "*/*",
    },
  };

  return await axios.post(url, JSON.stringify(data), config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/TMSIndiaDeviceToItemOrder";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQFvD4Q_4a8Ha9RrUj5Zff_DSNXm0Ie7E4vXBjFEAUqrTuOGxxMLRfnh1BeEoAdbcIPMDtancBjIkF4XFbbnrS36VpvTN";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

// Salesforce API request with Bearer token
const checkAntiFakeCode = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/TMSIndiaAntifakeCode";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQFvD4Q_4a8Ha9RrUj5Zff_DSNXm0Ie7E4vXBjFEAUqrTuOGxxMLRfnh1BeEoAdbcIPMDtancBjIkF4XFbbnrS36VpvTN";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

// Salesforce API request with Bearer token
const triggerAMCPaymentLink = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/IndiaAMCRetrigger";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQFvD4Q_4a8Ha9RrUj5Zff_DSNXm0Ie7E4vXBjFEAUqrTuOGxxMLRfnh1BeEoAdbcIPMDtancBjIkF4XFbbnrS36VpvTN";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

const convertAttachmentToPublicUrl = (internalUrl) => {
  if (internalUrl) {
    const string = internalUrl; //"org_14/capture_image_1124406008470300_uaJ1IeWPIghOVE60.png";
    return `${filesPrefixUrl}${string}`;
  } else {
    return "";
  }
};

const modifyFieldInMeta = (meta, fieldKey, newKeyValueObj) => {
  meta.map((singleField, index) => {
    if (singleField.key == fieldKey) {
      singleField = {
        ...singleField,
        ...newKeyValueObj,
      };
      meta[index] = singleField;
    }
  });
};

const getObjByKeyFrmArray = (array = [], key, value) => {
  return array.filter((singleObj) => singleObj[key] == value)?.[0];
};

function getUniqueEntries(array, keyName) {
  const uniqueEntries = [];
  const uniqueKeys = {};

  for (let i = 0; i < array.length; i++) {
    const obj = array[i];
    const key = obj[keyName];

    if (!uniqueKeys[key]) {
      // Add entry to the uniqueEntries array
      uniqueEntries.push(obj);
      // Add key to the uniqueKeys object
      uniqueKeys[key] = true;
    }
  }

  return uniqueEntries;
}

function disableMultipleFields(meta, labelToKeyMap, mandatoryLabels = []) {
  mandatoryLabels.forEach((singleField) => {
    modifyFieldInMeta(meta, labelToKeyMap[singleField], {
      disabled: true,
    });
  });
}

function hideMultipleFields(meta, labelToKeyMap, mandatoryLabels = []) {
  mandatoryLabels.forEach((singleField) => {
    modifyFieldInMeta(meta, labelToKeyMap[singleField], {
      hide: true,
    });
  });
}

function showMultipleFields(meta, labelToKeyMap, mandatoryLabels = []) {
  mandatoryLabels.forEach((singleField) => {
    modifyFieldInMeta(meta, labelToKeyMap[singleField], {
      hide: false,
    });
  });
}

function getCurrentTimestampInIST() {
  const now = new Date();
  const istOffset = 5.5 * 60 * 60 * 1000; // IST is UTC+5:30
  return now.getTime() + istOffset;
}

function canTriggerAMCLogic(triggerTimestampArr, currentTimestamp) {
  console.log("currentTimestamp in ist", currentTimestamp);
  const thirtySecondsInMillis = 30 * 1000;

  // Calculate the start of today in IST
  const startOfToday = new Date(currentTimestamp);
  console.log("startOfToday", startOfToday);
  startOfToday.setHours(0, 0, 0, 0);
  console.log("startOfToday", startOfToday);

  // Filter the triggers that happened today in IST
  const todayTriggers = triggerTimestampArr.filter((timestamp) => {
    const triggerTime = new Date(
      new Date(timestamp).getTime() + 5.5 * 60 * 60 * 1000
    );
    return triggerTime >= startOfToday;
  });
  console.log("todayTriggers", todayTriggers);

  // Check if we already have 5 triggers today
  if (todayTriggers.length >= 5) {
    return {
      status: false,
      message: "Retrigger limit reached for the day",
    };
  }

  // Check if the current trigger is at least 30 seconds after the last trigger
  if (triggerTimestampArr.length > 0) {
    const lastTriggerTime = new Date(
      triggerTimestampArr[triggerTimestampArr.length - 1]
    ).getTime();
    console.log(
      "lastTriggerTime ",
      lastTriggerTime,
      currentTimestamp,
      thirtySecondsInMillis,
      (currentTimestamp - lastTriggerTime) / 1000
    );
    if (currentTimestamp - lastTriggerTime < thirtySecondsInMillis) {
      return {
        status: false,
        message: "Please wait for 30 seconds before retriggering",
      };
    }
  }

  return {
    status: true,
    message: "Can retrigger amc link",
  };
}

function checkIfLineItemHasGKKEnabled(currentMeta, labelToKeyMap, allValues) {
  const lineItemsToCheck = ["Item 1", "Item 2", "Item 3", "Item 4"];
  const selectedLineItems = [];

  for (const key of lineItemsToCheck) {
    if (allValues[labelToKeyMap[key]]) {
      // Checks for non-empty and non-null values
      selectedLineItems.push(allValues[labelToKeyMap[key]]);
    }
  }
  console.log("selectedLineItems", selectedLineItems);
  if (selectedLineItems.length > 0 && currentMeta) {
    console.log("currentMeta-->", JSON.stringify(currentMeta));

    const lineItem1 = currentMeta.find(
      (item) => item.key == labelToKeyMap["Item 1"]
    );
    console.log("line item1", lineItem1);

    const lineItemOptions = lineItem1.options;
    console.log("line item options in func", lineItemOptions);

    const hasGkk = selectedLineItems.some((value) => {
      // Find the corresponding object in the options array
      console.log("value--", value, "lineitemoption--", lineItemOptions);
      const option = lineItemOptions.find((option) => option.value == value);
      console.log("option-->", option);
      // Check if the found object has is_gkk set to true
      return option && option.isGKK === "Yes";
    });
    console.log("has GKK in func", hasGkk);
    return hasGkk;
  } else {
    return false;
  }
}

function formatNumberWithSpaces(numberStr) {
  // Regular expression to check if the number is already formatted correctly
  const correctFormatRegex = /^(\d{4} \d{4} \d{4})$/;

  // If the number is already correctly formatted, return it as is
  if (correctFormatRegex.test(numberStr)) {
    return numberStr;
  }

  // Remove any existing spaces
  numberStr = numberStr.replace(/\s+/g, "");

  // Use a regular expression to match every 4 characters and add a space after them, but avoid adding space at the end
  return numberStr.replace(/(.{4})(?=.{4})/g, "$1 ");
}

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "No response received from Netsuite 1";

  const { meta, allValues, changedValues, request_data, currentMeta } = event;

  console.log("event", event);

  let disableFormSubmissionButton = false;

  const labelToKeyMap = {};

  meta.map((singleField) => {
    labelToKeyMap[singleField.label || singleField.cust_component_value] =
      singleField.key;
  });

  let customerMobileNo = request_data?.cust_mobile;

  let amcRetriggerCalls = request_data?.amc_retrigger_calls || [];

  let isGkkVerificationDone =
    request_data.Islockedforscratchcodevalidation &&
    request_data.Islockedforscratchcodevalidation.toLowerCase() == "yes";

  // console.log('event ',event);
  // console.log('event form_data',event.form_data);
  console.log("event request_data", request_data);

  let isDeviceBleEnabled =
    event.request_data["ec333b18-47eb-4eae-826a-1e33193a9068"]; //this is isBLEEnabled field from req data
  console.log("isDeviceBleEnabled -->>", isDeviceBleEnabled);

  let lineItemsMap = [
    {
      item_label: "Item 1",
      qty_label: "Quantity 1",
      price_label: "Price 1",
    },
    {
      item_label: "Item 2",
      qty_label: "Quantity 2",
      price_label: "Price 2",
    },
    {
      item_label: "Item 3",
      qty_label: "Quantity 3",
      price_label: "Price 3",
    },
    {
      item_label: "Item 4",
      qty_label: "Quantity 4",
      price_label: "Price 4",
    },
  ];
  let lineItemOptions = [];

  let isFirstTime = Object.keys(changedValues).length == 0;

  const fieldVsKey = {
    netsuiteOrderId: "79a88c7b-c64f-46c4-a277-bc80efa1c154",
    deviceCategoryId: "c0b457f1-67f2-4280-83b3-97fb0a64cb4f",
    amcPaymentDone: "5ede4ecb-abfc-4a39-953b-675a219696d4",
    amcId: "abaa261d-b394-4a1c-b6ce-49e88585d341",
  };

  const netsuiteOrderId = request_data[fieldVsKey.netsuiteOrderId];
  const deviceCategoryId = request_data[fieldVsKey.deviceCategoryId];

  let data = {
    deviceCategory: deviceCategoryId,
    Rfs_id: netsuiteOrderId,
  };

  // check if the API based data is already there
  if (isFirstTime) {
    // Get the data from API

    console.log("Netsuite api call data", data);

    let netsuiteLineItemOptions = [];
    try {
      const response = await makeRequest(data);
      console.log("Netsuite api response", response.data); // Handle the API response data
      if (response.data.status && response.data.message) {
        let { status, message } = response.data;
        if (status == "success") {
          netsuiteLineItemOptions = [...message];
        } else {
          console.log("error response from line item api", response.message);
        }
      } else {
        // this is a fatal error from netsuite
        // the response is not as per documentation rxd
        console.log("Fatal response received from Netsuite", response);
      }
    } catch (error) {
      logAxiosError(error);
      // handle error
    }

    const uniqueKeys = {};
    // console.log(`labelToKeyMap['Line item 1']`,labelToKeyMap['Line item 1'])
    lineItemOptions = netsuiteLineItemOptions.map((singleNetsuiteOption) => {
      let itemDescription = singleNetsuiteOption.item_description || "";
      return {
        value: singleNetsuiteOption.item_id,
        label: `${singleNetsuiteOption.itemName.trim()} ${itemDescription.trim()}`,
        price: singleNetsuiteOption.item_price,
        isGKK: singleNetsuiteOption?.is_gkk,
      };
    });
    lineItemOptions = [...getUniqueEntries(lineItemOptions, "value")];
  } else {
    let firstLineItemFieldMeta = getObjByKeyFrmArray(
      currentMeta,
      "key",
      labelToKeyMap[lineItemsMap[0].item_label]
    );
    console.log("firstLineItemFieldMeta", firstLineItemFieldMeta);
    lineItemOptions = [...firstLineItemFieldMeta.options];
  }

  const manipulatedFieldValues = {};

  let lineItemTotal = 0;

  let alreadySelectedItems = [];

  lineItemsMap.map((singleLineItem) => {
    // Setting line item options to line item 1
    modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.item_label], {
      options: lineItemOptions,
    });
    // Setting the price for individual selected line item
    let lineItem = allValues[labelToKeyMap[singleLineItem.item_label]];
    // Check if this item was already selected
    if (alreadySelectedItems.includes(lineItem)) {
      // reset this line item field
      lineItem = undefined;
      manipulatedFieldValues[labelToKeyMap[singleLineItem.item_label]] = "";
    } else {
      alreadySelectedItems.push(lineItem);
    }

    if (lineItem) {
      // set price
      const currentItemPriceInField =
        allValues[labelToKeyMap[singleLineItem.price_label]];

      const itemPrice =
        currentItemPriceInField !== undefined && currentItemPriceInField > 0
          ? currentItemPriceInField
          : getObjByKeyFrmArray(lineItemOptions, "value", lineItem)?.price;

      if (currentItemPriceInField == undefined || itemPrice) {
        manipulatedFieldValues[labelToKeyMap[singleLineItem.price_label]] =
          itemPrice || 0;
      }

      // Adding to total
      let itemQty = allValues[labelToKeyMap[singleLineItem.qty_label]];

      if (itemQty != null && itemQty <= 0) {
        itemQty = 1;
        manipulatedFieldValues[labelToKeyMap[singleLineItem.qty_label]] = 1;
      }

      if (itemPrice && itemQty) {
        lineItemTotal = lineItemTotal + itemPrice * itemQty;
      }
      // make the qty field not mandatory for item which is not selected
      modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.qty_label], {
        required: true,
      });
    } else {
      // make the qty field not mandatory for item which is not selected
      modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.qty_label], {
        required: false,
      });
      manipulatedFieldValues[labelToKeyMap[singleLineItem.qty_label]] = "";
      manipulatedFieldValues[labelToKeyMap[singleLineItem.price_label]] = "";
    }
  });

  modifyFieldInMeta(meta, labelToKeyMap["Item 1"], {
    required: true,
  });

  modifyFieldInMeta(meta, labelToKeyMap["Quantity 1"], {
    required: true,
  });

  let deliveryCharges = allValues[labelToKeyMap["Delivery Charges"]];
  if (deliveryCharges > 0) {
    lineItemTotal = lineItemTotal + deliveryCharges;
  }

  manipulatedFieldValues[labelToKeyMap["Total RFS Value"]] = lineItemTotal;
  modifyFieldInMeta(meta, labelToKeyMap["Total RFS Value"], {
    disabled: true,
  });

  modifyFieldInMeta(meta, labelToKeyMap["Order Closure Confirmation Code"], {
    hide: request_data["configure_consumer_otp_verification"] ? true : false,
  });
  modifyFieldInMeta(meta, "b66d4af1-d51c-434a-8888-a226a1011730", {
    hide: request_data["configure_consumer_otp_verification"] ? true : false,
  });

  //Scratch code starts

  let errorMessage;

  modifyFieldInMeta(meta, labelToKeyMap["GKK Verified"], {
    disabled: true,
    hide: true,
  });

  let isGKKEnabled = checkIfLineItemHasGKKEnabled(
    currentMeta,
    labelToKeyMap,
    allValues
  );
  console.log("isGKKEnabled--> ", isGKKEnabled);
  modifyFieldInMeta(meta, labelToKeyMap["PUREIT MACHINE"], {
    hide: true,
  });

  if (!isGKKEnabled) {
    modifyFieldInMeta(meta, labelToKeyMap["GKK Scratch Code"], {
      hide: true,
    });
    modifyFieldInMeta(meta, labelToKeyMap["GACC"], {
      hide: true,
    });
  } else if (isGKKEnabled) {
    modifyFieldInMeta(meta, labelToKeyMap["GKK Scratch Code"], {
      hide: false,
    });
    modifyFieldInMeta(meta, labelToKeyMap["GACC"], {
      hide: false,
    });
  }

  function syncGKKVerified() {
    modifyFieldInMeta(meta, labelToKeyMap["GKK Scratch Code"], {
      hide: true,
    });
    modifyFieldInMeta(meta, labelToKeyMap["GACC"], {
      hide: true,
    });
    modifyFieldInMeta(meta, labelToKeyMap["GKK Verified"], {
      hide: false,
    });
    if (isDeviceBleEnabled.toLowerCase() == "yes") {
      modifyFieldInMeta(meta, labelToKeyMap["PUREIT MACHINE"], {
        hide: false,
      });
    }
  }

  if (allValues[labelToKeyMap["GKK Verified"]] == "Yes") {
    syncGKKVerified();
  }

  if (isGkkVerificationDone) {
    manipulatedFieldValues[labelToKeyMap["GKK Verified"]] = "Yes";
    syncGKKVerified();
    //disable fields
    disableMultipleFields(meta, labelToKeyMap, [
      "Item 1",
      "Quantity 1",
      "Price 1",
      "Item 2",
      "Quantity 2",
      "Price 2",
      "Item 3",
      "Quantity 3",
      "Price 3",
      "Item 4",
      "Quantity 4",
      "Price 4",
    ]);
    //prefill locked line items from request data
    let lockedLineItemsData = request_data.lock_lineitem || [];
    let lockedLineItemTotal = 0;
    console.log("lockedLineItemsData : ", lockedLineItemsData);
    let counter = 1;
    lockedLineItemsData.map((singleLockedLineItem) => {
      manipulatedFieldValues[labelToKeyMap[`Item ${counter}`]] =
        singleLockedLineItem.item;
      manipulatedFieldValues[labelToKeyMap[`Quantity ${counter}`]] =
        singleLockedLineItem.quantity;
      manipulatedFieldValues[labelToKeyMap[`Price ${counter}`]] =
        singleLockedLineItem.price;
      lockedLineItemTotal =
        lockedLineItemTotal +
        Number(singleLockedLineItem.quantity) *
          Number(singleLockedLineItem.price);
      counter++;
    });
    if (deliveryCharges > 0) {
      lockedLineItemTotal = lockedLineItemTotal + deliveryCharges;
    }
    manipulatedFieldValues[labelToKeyMap["Total RFS Value"]] =
      lockedLineItemTotal;
  }

  if (allValues[labelToKeyMap["GKK Scratch Code"]]?.length > 0) {
    modifyFieldInMeta(meta, labelToKeyMap["GKK Scratch Code"], {
      label: "All line items will be locked on scratch code validation",
    });
  }

  if (
    allValues[labelToKeyMap["GKK Verified"]] != "Yes" &&
    allValues[labelToKeyMap["GKK Scratch Code"]] &&
    allValues[labelToKeyMap["GACC"]]?.length == 6
  ) {
    let formattedScratchCode = formatNumberWithSpaces(
      allValues[labelToKeyMap["GKK Scratch Code"]]
    );
    manipulatedFieldValues[labelToKeyMap["GKK Scratch Code"]] =
      formattedScratchCode;
    let itemDataFrNetsuite = [];

    lineItemsMap.map((singleLineItemMap) => {
      const item = allValues[labelToKeyMap[singleLineItemMap.item_label]];
      if (item) {
        itemDataFrNetsuite.push({
          item,
          quantity: allValues[labelToKeyMap[singleLineItemMap.qty_label]],
          price: allValues[labelToKeyMap[singleLineItemMap.price_label]] + "",
        });
      }
    });
    console.log("line item data for antifake api: ", itemDataFrNetsuite);
    let data = {
      rfs_id: netsuiteOrderId,
      scratch_code_number: formattedScratchCode,
      antiflakeCode: allValues[labelToKeyMap["GACC"]],
      lineItemData: itemDataFrNetsuite,
      //add here
    };

    try {
      console.log("salesforce antifake api data", data);
      const response = await checkAntiFakeCode(data);
      console.log("salesforce antifake api response", response.data); // Handle the API response data
      if (response.data.status && response.data.message) {
        let { status, message } = response.data;
        if (status.toLowerCase() === "success") {
          manipulatedFieldValues[labelToKeyMap["GKK Verified"]] = "Yes";
          syncGKKVerified();
        } else {
          console.log("error response from antifake api", message);
          errorMessage = {
            duration: 3,
            message,
          };
        }
      } else {
        // this is a fatal error from salesforce
        // the response is not as per documentation rxd
        console.log("Fatal response received from salesforce", response);
      }
    } catch (error) {
      logAxiosError(error);
      // handle error
    }
  }

  let pureitMachineBleValue = allValues[labelToKeyMap["PUREIT MACHINE"]];
  console.log("pureitMachineBleValue ---->", pureitMachineBleValue);
  if (pureitMachineBleValue) {
    disableFormSubmissionButton = false;
  }
  console.log("disableFormSubmission", disableFormSubmissionButton);

  hideMultipleFields(meta, labelToKeyMap, [
    "Mobile Number",
    "Retrigger",
    "AMC Link Retrigger",
  ]);
  let amcPaymentsNeedRetrigger = request_data[fieldVsKey.amcPaymentDone];
  let is_customer_interested_to_purchase_amc =
    request_data?.is_customer_interested_to_purchase_amc;

  let responseStatusAMC = false;
  let responseMessageAMC = "AMC Link failed to trigger.";
  let responseStatusTMS = false;
  let responseMessageTMS = "Cannot update request";

  let showErrorForRetriggerFailure = false;

  if (
    is_customer_interested_to_purchase_amc == "Yes" &&
    amcPaymentsNeedRetrigger != "Yes"
  ) {
    let currentTimestamp = getCurrentTimestampInIST();
    const date = new Date(currentTimestamp);
    const timestampFrReqData = date.toISOString();
    let canRetrigger = canTriggerAMCLogic(amcRetriggerCalls, currentTimestamp);
    if (amcRetriggerCalls.length == 0 || canRetrigger.status) {
      showMultipleFields(meta, labelToKeyMap, [
        "Mobile Number",
        "Retrigger",
        "AMC Link Retrigger",
      ]);

      //checking if the mobile number is updated once from request data or not
      if (
        !allValues.hasOwnProperty(labelToKeyMap["Mobile Number"]) &&
        allValues[labelToKeyMap["Mobile Number"]] !== ""
      ) {
        manipulatedFieldValues[labelToKeyMap["Mobile Number"]] =
          customerMobileNo;
      }
      if (changedValues[labelToKeyMap["Retrigger"]] == true) {
        showErrorForRetriggerFailure = true;
        let amcData = {
          request_type: "AMC_RETRIGGER",
          mobile_number: allValues[labelToKeyMap["Mobile Number"]],
          rfs_for_amc_internal_id: request_data[fieldVsKey.amcId],
          comments_for_retrigger: "",
        };

        console.log("salesforce api call data", amcData);

        try {
          const response = await triggerAMCPaymentLink(amcData);
          console.log("salesforce amc link api api response", response.data); // Handle the API response data

          const salesforceData = response?.data[0] || response.data;
          console.log("salesforceData ", salesforceData);
          if (salesforceData && salesforceData.message) {
            let { message, responseCode, status } = salesforceData;
            responseMessageAMC = message;

            if (status == "Success") {
              responseStatusAMC = true;
            } else {
              responseMessageAMC = message;
            }
          } else {
            // this is a fatal error from salesforce
            // the response is not as per documentation rxd
            responseMessage = JSON.stringify(response.data);
          }
        } catch (error) {
          logAxiosError(error);
          // handle error
        }

        if (responseStatusAMC) {
          //Call TMS API to update triggers
          amcRetriggerCalls.push(timestampFrReqData);
          let updateSrvcReqData = {
            batch_data: [
              {
                "79a88c7b-c64f-46c4-a277-bc80efa1c154": netsuiteOrderId,
                amc_retrigger_calls: amcRetriggerCalls,
              },
            ],
          };
          // //run tms update api to update req
          try {
            console.log("TMS api data", JSON.stringify(updateSrvcReqData));
            const response = await updateSrvcReqonTMS(updateSrvcReqData);
            console.log("TMS api response", response.data); // Handle the API response data
            const tmsResponse = response.data;
            if (tmsResponse.status == "success") {
              console.log("update hogaya", response.data);
              responseStatusTMS = true;
              responseMessageTMS = JSON.stringify(response.message);
            } else {
              responseStatusTMS = true;
              responseMessageTMS = JSON.stringify(response.message);
              console.log("cannot run api");
            }
          } catch (error) {
            // logAxiosError(error);
            console.error("Error in handler:", error);
            responseMessageTMS = "An error occurred: " + error.message;
            // handle error
          }
        }
      }
    } else {
      errorMessage = {
        duration: 3,
        message: canRetrigger.message,
      };
    }
    if (
      showErrorForRetriggerFailure &&
      (!responseStatusAMC || !responseStatusTMS)
    ) {
      let message = !responseStatusAMC
        ? responseMessageAMC
        : responseMessageTMS;
      errorMessage = {
        duration: 3,
        message,
      };
      disableFormSubmissionButton = true;
    }
  }

  responseStatus = true;
  responseMessage = "success";
  const response = {
    status: responseStatus,
    message: responseMessage,
    data: {
      meta,
      allValues,
      changedValues,
      manipulatedFieldValues,
      disableFormSubmissionButton,
      errorMessage,
    },
  };
  return response;
};

// handler({});

exports.handler = handler;
