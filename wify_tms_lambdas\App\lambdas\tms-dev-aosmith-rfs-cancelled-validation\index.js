const axios = require("axios");

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/TMSIndiaOrderClosure";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQFvD4Q_4a8Ha9RrUj5Zff_DSNXm0Ie7E4vXBjFEAUqrTuOGxxMLRfnh1BeEoAdbcIPMDtancBjIkF4XFbbnrS36VpvTN";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

const convertAttachmentToPublicUrl = (internalUrl) => {
  if (internalUrl) {
    const string = internalUrl; //"org_14/capture_image_1124406008470300_uaJ1IeWPIghOVE60.png";
    return `${filesPrefixUrl}${string}`;
  } else {
    return "";
  }
};

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "No response received from Salesforce";

  console.log("event form_data", event.form_data);
  console.log("event request_data", event.request_data);

  const optionsFieldMap = {
    "Duplicate entry": "3a723884-45cc-43da-ab00-6a7d184c2390",
    "Continuous door lock. Door lock card/ SMS dropped":
      "35a8872b-4b01-4c99-8dd3-42aebc947b7a",
    "Customer cancelled Call:Refused to pay":
      "7afb025a-f567-4fe0-80f9-429fc5d42450",
    "Customer gave future appointment": "619d04a7-0ac9-48b5-ab5e-4ea4cc1d00bb",
    "Customer refused for the service and not using the device":
      "c55cfdb8-9420-4d4a-98a3-229ca3a3cf3d",
    "Customer shifted residence": "0afc65a3-d81c-4ab3-b81f-b33094ec42f0",
    "Device outside service area jurisdiction":
      "f3ef202f-e230-4975-8bc6-c0cae8493cd8",
    "Not Contactable & address not found":
      "e10a7f69-2e9f-40e9-a9c5-bd00bc976291",
  };

  const cancellationOptionsvsCode = {
    "Duplicate entry": 1,
    "Continuous door lock. Door lock card/ SMS dropped": 2,
    "Customer cancelled Call:Refused to pay": 3,
    "Customer gave future appointment": 4,
    "Customer refused for the service and not using the device": 5,
    "Customer shifted residence": 6,
    "Device outside service area jurisdiction": 7,
    "Not Contactable & address not found": 8,
  };

  const getKeyByValue = (object, value) => {
    return Object.keys(object).find((key) => object[key] === value);
  };
  const getOptionValueByKey = (
    key,
    optionsFieldMap,
    cancellationOptionsvsCode
  ) => {
    return cancellationOptionsvsCode[getKeyByValue(optionsFieldMap, key)];
  };

  const fieldVsKey = {
    netsuiteOrderId: "79a88c7b-c64f-46c4-a277-bc80efa1c154",
    "Reason for pending": "5f362646-8243-4ed7-b26e-20eecbc98dd5",
    "Reason for cancellation": "5aa4c89d-d3b6-4ab2-95a0-8dbeaab15024",
  };
  const netsuiteOrderId = event.request_data[fieldVsKey.netsuiteOrderId];

  const reasonForcancellation = getOptionValueByKey(
    event.form_data[fieldVsKey["Reason for cancellation"]],
    optionsFieldMap,
    cancellationOptionsvsCode
  );
  const attachments = event.form_data.attachments?.general || [];
  const finalAttachments = attachments.map((url) => filesPrefixUrl + url);

  let data = {
    rfsid: netsuiteOrderId,
    Delivery_dispostion: 98, //98 is for pending
    isDelivered: "F", //sending false for pending
    reason_for_Pending: "",
    reason_for_cancellation: reasonForcancellation,
    Close_job_attachments: finalAttachments,
    Items_Data: [],
    remarks: event.form_data.remarks,
  };

  console.log("salesforce api call data", data);
  try {
    const response = await makeRequest(data);
    console.log("salesforce api response", response.data); // Handle the API response data
    if (response.data && response.data.message) {
      let { status, responseCode, message } = response.data;
      responseMessage = message;
      console.log(
        "message,responseCode,details : ",
        message,
        responseCode,
        status
      );
      if (status == "success") {
        responseStatus = true; //
      }
    } else {
      // this is a fatal error from salesforce
      // the response is not as per documentation rxd
      responseMessage = JSON.stringify(response.data);
    }
  } catch (error) {
    logAxiosError(error);
    // handle error
  }

  // if(data.remarks)
  // TODO implement
  const response = {
    status: responseStatus,
    message: responseMessage,
  };
  return response;
};

// handler({});

exports.handler = handler;
