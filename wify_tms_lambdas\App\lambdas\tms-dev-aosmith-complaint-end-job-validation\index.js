const axios = require("axios");

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/TMSIndiaCaseClosure";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQFvD4Q_4a8Ha9RrUj5Zff_DSNXm0Ie7E4vXBjFEAUqrTuOGxxMLRfnh1BeEoAdbcIPMDtancBjIkF4XFbbnrS36VpvTN";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

function getKeyByValue(data, value) {
  return Object.keys(data).find((key) => data[key] === value);
}

const convertAttachmentToPublicUrl = (internalUrl) => {
  if (internalUrl) {
    const string = internalUrl; //"org_14/capture_image_1124406008470300_uaJ1IeWPIghOVE60.png";
    return `${filesPrefixUrl}${string}`;
  } else {
    return "";
  }
};

const getOptionValueByKey = (key, optionsFieldMap, pendingOptionsvsCode) => {
  return pendingOptionsvsCode[getKeyByValue(optionsFieldMap, key)];
};

const isNotNumber = (value) => typeof value !== "number" || isNaN(value);

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "No response received from Salesforce";

  console.log("event form_data", event.form_data);
  console.log("event request_data", event.request_data);

  let lineItemsMap = [
    {
      item_label: "Item 1",
      qty_label: "Quantity 1",
      price_label: "Price 1",
      is_claimable: "Is claimable 1",
      is_claimable_yes_key: "a486b1d6-463d-43b7-b40d-577c566c1cf2",
      is_claimable_no_key: "17207c49-9b68-4b9b-84db-de748100af35",
    },
    {
      item_label: "Item 2",
      qty_label: "Quantity 2",
      price_label: "Price 2",
      is_claimable: "Is claimable 2",
      is_claimable_yes_key: "02868969-a028-4f66-83ff-e3778e0e3c0d",
      is_claimable_no_key: "efd2197b-d3cf-416b-8c3a-3949ff71a2c8",
    },
    {
      item_label: "Item 3",
      qty_label: "Quantity 3",
      price_label: "Price 3",
      is_claimable: "Is claimable 3",
      is_claimable_yes_key: "5d64f0a6-a2cf-462c-bf2a-767ae50d2db7",
      is_claimable_no_key: "1d9d43a9-97d5-4f91-a425-eb7fb3e1da2f",
    },
    {
      item_label: "Item 4",
      qty_label: "Quantity 4",
      price_label: "Price 4",
      is_claimable: "Is claimable 4",
      is_claimable_yes_key: "6aeaf693-3eba-4df8-860d-00431db17ac2",
      is_claimable_no_key: "aecc9edd-e7ce-4df1-a72f-642a9c4a2200",
    },
  ];

  const boosterpumpOptionsMap = {
    Yes: "7b02aab4-a157-4ac4-bc34-161f2a98368a",
    No: "1a93f3d7-3124-46c6-a831-e8d9087d6cd6",
  };

  const prvInstalledMap = {
    Yes: "8e74519a-bad6-4fb3-b515-b762abaa630a",
    No: "2bab62e7-3686-44a6-83b4-9dc2ecef9acc",
  };

  const sourceOfSaleOptionsMap = {
    "DTC-WEB": "d8ccf60f-24a4-4712-b5f7-8f080f2d098e",
    "E-Commerce": "763357fe-9d47-43e5-acd9-1247a5593da0",
    GT: "449020ee-b64f-47ae-986e-a4f3a7c2d7ae",
    LSP: "109aa79a-00f9-4b48-9ac0-41faf0876e70",
    MT: "2513afa7-e934-42f5-97c9-f05b5a70b267",
    Others: "90d6c5cf-add8-4738-bcaf-542c4ab18386",
    PPS: "14cdffe0-8ec6-4afc-b099-8fb61dea7539",
  };

  const sourceOfSaleValueMap = {
    "DTC-WEB": 7,
    "E-Commerce": 3,
    GT: 2,
    LSP: 4,
    MT: 1,
    Others: 5,
    PPS: 6,
  };

  const fieldVsKey = {
    netsuiteOrderId: "79a88c7b-c64f-46c4-a277-bc80efa1c154",
    "Complaint Title": "730518bb-a871-46dd-b241-6622d04da954",
    Description: "6a75d297-946d-4ea6-9902-a3ffaa18341b",
    "Part name": "7b51edca-0115-44cb-9b12-56833d9db3e3",
    "Action Taken": "b264a495-e0b7-4310-bc87-de0b6cc4f50d",
    "Item 1": "8ff5d752-be57-4acb-9aaf-1d1269f698ad",
    "Is claimable 1": "d9d562e8-d795-4aca-874e-e05e406e9dbf",
    "Quantity 1": "e205f833-0fc8-4abb-91d0-54b7fc885ba0",
    "Price 1": "ad57051f-b30e-4eea-bf33-4e8b4d66eca3",
    "Item 2": "f5a5696b-c9e2-452c-ae07-c376dbea8535",
    "Is claimable 2": "502a26fc-7601-40c1-af25-5b3fcd758bc8",
    "Quantity 2": "66a6b361-c0f7-47dd-bda2-110cd2839815",
    "Price 2": "c9fca07f-3f2a-498e-928d-123a92b828ef",
    "Item 3": "d4c36f94-3e66-4303-9e4a-8b23cf9e144b",
    "Is claimable 3": "a9b338ec-ba3a-4053-aca2-14668b37d1d5",
    "Quantity 3": "6a04c545-98e0-4bca-90dd-a5c284b7ff9d",
    "Price 3": "2864cce4-1088-4c32-9ed9-db66bc288331",
    "Item 4": "75ff8787-c483-4e5f-a52a-51d314eeb2cf",
    "Is claimable 4": "8c05ae6a-deb0-480b-b8fb-c1e555c9d70c",
    "Quantity 4": "b2329736-fedd-4bcd-85ff-6ed46a19faf6",
    "Price 4": "eead51f5-ccb7-40bc-98cc-9d678a9d23d4",
    "Visit Charge": "4d889185-900a-4cbf-b9dc-c67b1e178a4f",
    "Total Amount Collected": "86e3d911-11cb-4964-8a88-92b23b7b973a",
    "No. of stories building": "0d06f3c9-b033-417c-a5c8-a1cc81dfd41d",
    "Customer residing on Floor no.": "fdc78d13-52f1-4956-9048-d17f60d7d4d7",
    "Location of water tank or overhead tank":
      "a02283ce-bf74-4519-91fc-78eb39e6fcb5",
    "Length of tube required(if more than 2m)":
      "bb503fde-65dd-48e3-91d8-9a1cb3cfbfa2",
    "Inlet water pressure": "9a718a17-6867-4284-a707-946231b0b12b",
    "PRV Installed": "22c64c58-c001-47fa-a302-bd8970e97dcb",
    "Source of Sale": "33586502-2120-426f-b449-c6240e097e70",
    "Boosterpump fixed due to low I/P Water pressure":
      "a9166335-8ba1-4081-8d3b-fa4f37875f23",
    "Check the inlet water TDS level by TDS meter":
      "380e008f-53d4-4c25-828c-cbb2c98aa71f",
    "Output TDS level": "5ffc3ec3-4c30-4ce5-b2c6-020e533ffd4b",
    "Service invoice image": "fee82b44-06ae-48d1-b8ec-29c7ec3aa439",
    "After service image": "44bdb108-2fe3-4221-8a78-3c766b50631b",
    "Device invoice image": "64efbd54-dc7e-4330-b792-278c689f2a19",
  };

  const netsuiteOrderId = event.request_data[fieldVsKey.netsuiteOrderId];
  const afterServiceImage = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey["After service image"]]?.[0]
  );
  const invoiceImage = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey["Service invoice image"]]?.[0]
  );
  const deviceInvoiceImage = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey["Device invoice image"]]?.[0]
  );
  const totalAmountCollected =
    event.form_data[fieldVsKey["Total Amount Collected"]];

  const sourceOfSales = getOptionValueByKey(
    event.form_data[fieldVsKey["Source of Sale"]],
    sourceOfSaleOptionsMap,
    sourceOfSaleValueMap
  );

  const attachments = event.form_data.attachments?.general || [];
  const finalAttachments = attachments.map((url) => filesPrefixUrl + url);

  let itemDataFrNetsuite = [];

  lineItemsMap.map((singleLineItemMap) => {
    const item = event.form_data[fieldVsKey[singleLineItemMap.item_label]];
    if (item) {
      itemDataFrNetsuite.push({
        item,
        quantity: event.form_data[fieldVsKey[singleLineItemMap.qty_label]],
        price: event.form_data[fieldVsKey[singleLineItemMap.price_label]],
        is_claimble:
          event.form_data[fieldVsKey[singleLineItemMap.is_claimable]] ==
          singleLineItemMap.is_claimable_yes_key
            ? true
            : false,
      });
    }
  });

  let data = {
    case_id: netsuiteOrderId,
    status: 5,
    complaint_title: event.form_data[fieldVsKey["Complaint Title"]],
    description: event.form_data[fieldVsKey["Description"]],
    part_name: event.form_data[fieldVsKey["Part name"]],
    action_taken_id: event.form_data[fieldVsKey["Action Taken"]],
    action_taken_name: "",
    visit_charge: event.form_data[fieldVsKey["Visit Charge"]],
    total_amount_collected: totalAmountCollected,
    no_of_stories_building:
      event.form_data[fieldVsKey["No. of stories building"]],
    customer_residing_on_floor_no:
      event.form_data[fieldVsKey["Customer residing on Floor no."]],
    location_of_water_tank_or_overhead_tank:
      event.form_data[fieldVsKey["Location of water tank or overhead tank"]],
    length_of_tube_required:
      event.form_data[fieldVsKey["Length of tube required(if more than 2m)"]],
    inlet_water_pressure: event.form_data[fieldVsKey["Inlet water pressure"]],
    prv_installed: getKeyByValue(
      prvInstalledMap,
      event.form_data[fieldVsKey["PRV Installed"]]
    ),
    in_source_of_sale: sourceOfSales,
    boosterpump_fixed_due_to_low_ip_waterpre: getKeyByValue(
      boosterpumpOptionsMap,
      event.form_data[
        fieldVsKey["Boosterpump fixed due to low I/P Water pressure"]
      ]
    ),
    check_the_inletwatertdslevel_by_tdsmeter:
      event.form_data[
        fieldVsKey["Check the inlet water TDS level by TDS meter"]
      ],
    output_tds_level: event.form_data[fieldVsKey["Output TDS level"]],
    Items_Data: itemDataFrNetsuite,
    delivery_charges: event.form_data[fieldVsKey["Visit Charge"]],
    closure_confirmation_code: event.form_data.otp,
    service_invoice_img: invoiceImage,
    after_service_img: afterServiceImage,
    device_invoice_img: deviceInvoiceImage,
    remarks: event.form_data.remarks,
    Close_job_attachments: finalAttachments,
  };

  if (isNotNumber(data.action_taken_id)) {
    console.log("Invalid Action Taken Value", data.action_taken_id);
    return {
      status: false,
      message: "Invalid Action Taken Value. Please close the form & retry.",
    };
  }

  console.log("Salesforce api call data", data);
  try {
    const response = await makeRequest(data);
    console.log("Salesforce api response", response.data); // Handle the API response data
    const salesforceData = response.data.data?.[0] || response.data;
    if (salesforceData && salesforceData.message) {
      let { status, responseCode, message } = response.data;
      responseMessage = message;
      console.log(
        "message,responseCode,details : ",
        message,
        responseCode,
        status
      );
      if (status == "success") {
        responseStatus = true; //
      }
    } else {
      // this is a fatal error from salesforce
      // the response is not as per documentation rxd
      responseMessage = JSON.stringify(response.data);
    }
  } catch (error) {
    logAxiosError(error);
    // handle error
  }

  // if(data.remarks)
  // TODO implement
  const response = {
    status: responseStatus,
    message: responseMessage,
  };
  return response;
};

// handler({});

exports.handler = handler;
