const axios = require("axios");

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/TMSIndiaCaseClosure";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQFvD4Q_4a8Ha9RrUj5Zff_DSNXm0Ie7E4vXBjFEAUqrTuOGxxMLRfnh1BeEoAdbcIPMDtancBjIkF4XFbbnrS36VpvTN";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

const convertAttachmentToPublicUrl = (internalUrl) => {
  if (internalUrl) {
    const string = internalUrl; //"org_14/capture_image_1124406008470300_uaJ1IeWPIghOVE60.png";
    return `${filesPrefixUrl}${string}`;
  } else {
    return "";
  }
};

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "No response received from Salesforce";

  console.log("event form_data", event.form_data);
  console.log("event request_data", event.request_data);

  const optionsFieldMap = {
    "Duplicate entry": "76b3f12f-ddbc-4e21-8d65-3ce48a8437ac",
    "Continuous door lock. Door lock card/ SMS dropped":
      "f5e12e38-d96b-4df3-af39-1eab5e08de38",
    "Customer cancelled Call:Refused to pay":
      "8cbb8fd5-c888-43a6-920d-246689f37e6a",
    "Customer gave future appointment": "be139208-d0ec-4e9b-9183-e799b61835fb",
    "Customer refused for the service and not using the device":
      "88fae78f-5d0a-423b-b1c7-a9a87746e6b7",
    "Customer shifted residence": "5ddbd00e-ef64-4131-9da0-69345e5af8a1",
    "Device outside service area jurisdiction":
      "2cfef66d-6819-4a59-9e9f-73b29c35ab45",
    "Not Contactable & address not found":
      "b0a053a5-ffee-431b-80f9-50d4822e48b1",
  };

  const cancellationOptionsvsCode = {
    "Duplicate entry": 1,
    "Continuous door lock. Door lock card/ SMS dropped": 2,
    "Customer cancelled Call:Refused to pay": 3,
    "Customer gave future appointment": 4,
    "Customer refused for the service and not using the device": 5,
    "Customer shifted residence": 6,
    "Device outside service area jurisdiction": 7,
    "Not Contactable & address not found": 8,
  };

  const getKeyByValue = (object, value) => {
    return Object.keys(object).find((key) => object[key] === value);
  };
  const getOptionValueByKey = (
    key,
    optionsFieldMap,
    cancellationOptionsvsCode
  ) => {
    return cancellationOptionsvsCode[getKeyByValue(optionsFieldMap, key)];
  };

  const fieldVsKey = {
    netsuiteOrderId: "79a88c7b-c64f-46c4-a277-bc80efa1c154",
    "Reason for cancellation": "6bc6f0a4-7230-4718-b4e8-f2cd9ca422e0",
  };
  const netsuiteOrderId = event.request_data[fieldVsKey.netsuiteOrderId];

  const reasonForcancellation = getOptionValueByKey(
    event.form_data[fieldVsKey["Reason for cancellation"]],
    optionsFieldMap,
    cancellationOptionsvsCode
  );

  const attachments = event.form_data.attachments?.general || [];
  const finalAttachments = attachments.map((url) => filesPrefixUrl + url);

  let data = {
    case_id: netsuiteOrderId,
    status: 6, //6 is for cancelled
    reason_for_complaint_cancellation: reasonForcancellation,
    Close_job_attachments: finalAttachments,
    remarks: event.form_data.remarks,
  };

  console.log("salesforce api call data", data);
  try {
    const response = await makeRequest(data);
    console.log("salesforce api response", response.data); // Handle the API response data
    if (response.data && response.data.message) {
      let { status, responseCode, message } = response.data;
      responseMessage = message;
      console.log(
        "message,responseCode,details : ",
        message,
        responseCode,
        status
      );
      if (status == "success") {
        responseStatus = true; //
      }
    } else {
      // this is a fatal error from salesforce
      // the response is not as per documentation rxd
      responseMessage = JSON.stringify(response.data);
    }
  } catch (error) {
    logAxiosError(error);
    // handle error
  }

  // if(data.remarks)
  // TODO implement
  const response = {
    status: responseStatus,
    message: responseMessage,
  };
  return response;
};

// handler({});

exports.handler = handler;
